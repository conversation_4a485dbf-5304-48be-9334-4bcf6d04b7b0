package acceptance_test.WorldTests;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.Position;
import za.co.wethinkcode.robots.config.Config;
import za.co.wethinkcode.robots.maze.Maze;
import za.co.wethinkcode.robots.obstacle.Obstacle;
import za.co.wethinkcode.robots.world.World;
import za.co.wethinkcode.robots.world.WorldGUI;

import java.lang.reflect.Field;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

// Test class for world initialization acceptance tests
public class WorldInitializationAcceptanceTest {

    private World world;
    private String originalObstacleMode;
    private int originalHeight;
    private int originalWidth;

    // Setup method to initialize the world with consistent configuration
    @BeforeEach
    void setUp() throws Exception {
        // Save original config values to restore later
        originalObstacleMode = Config.OBSTACLE_MODE;
        originalHeight = Config.HEIGHT;
        originalWidth = Config.WIDTH;

        // Force reload config to ensure clean state
        Config.loadConfig("config.properties");

        // Override with test-specific values
        setConfigField("HEIGHT", 20);
        setConfigField("WIDTH", 20);
        setConfigField("OBSTACLE_MODE", "BP-10,10:11,11");

        // Initialize world with GUI enabled and specific maze configuration
        world = new World(true, "BP-10,10:11,11");
    }

    // Cleanup method to restore original configuration
    @AfterEach
    void tearDown() throws Exception {
        // Restore original config values to prevent affecting other tests
        try {
            setConfigField("OBSTACLE_MODE", originalObstacleMode);
            setConfigField("HEIGHT", originalHeight);
            setConfigField("WIDTH", originalWidth);
        } catch (Exception e) {
            // If restoration fails, reload the original config
            Config.loadConfig("config.properties");
        }
        world = null;
    }

    // Helper method to set Config fields using reflection
    private void setConfigField(String fieldName, Object value) throws Exception {
        Field field = Config.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(null, value);
    }

    // Helper method to count bottomless pit obstacles in OBSTACLE_MODE
    private int countBottomlessPits(String obstacleMode) {
        String[] obstacles = obstacleMode.split(" ");
        int count = 0;
        for (String obstacle : obstacles) {
            if (obstacle.startsWith("BP")) {
                count++;
            }
        }
        return count;
    }

    // Helper method to get obstacles in OBSTACLE_MODE
    private String getObstacle(String obstacleMode, String obstacleType) {
        String[] obstacles = obstacleMode.split(" ");

        for (String obstacle : obstacles) {
            if (obstacle.startsWith("BP") && obstacleType.equals("BP")) {
                return "BOTTOMLESS_PIT";
            }
        }
        return "Not found!";
    }

    // ========================================
    // Scenario a: World initialization
    // ========================================
    @Test
    @DisplayName("AT-5.1.1: Test world initialization with GUI and obstacles")
    void testWorldInitialization() {
        // When a new world is created with GUI enabled
        assertNotNull(world, "World object should not be null");

        // Then the world should set boundaries from (0,0) to (HEIGHT-1, WIDTH-1)
        assertEquals(new Position(0, 0), world.getTOP_LEFT(), "World top-left boundary should be (0,0)");
        assertEquals(new Position(Config.WIDTH - 1, Config.HEIGHT - 1), world.getBOTTOM_RIGHT(),
                "World bottom-right boundary should be (19,19)");

        // And obstacles should be loaded from the maze configuration
        List<Obstacle> obstacles = world.getMaze().getObstacles();
        assertNotNull(obstacles, "Obstacles list should not be null");
        assertEquals(1, countBottomlessPits(Config.OBSTACLE_MODE), "Expected one bottomless pit from OBSTACLE_MODE");
        assertEquals("BOTTOMLESS_PIT", getObstacle(Config.OBSTACLE_MODE, "BP"), "Obstacle should be a bottomless pit");

        // And an empty robot list should be initialized
        assertNotNull(world.getBots(), "Robot list should not be null");
        assertTrue(world.getBots().isEmpty(), "Robot list should be initialized empty");

        // And a WorldGUI should be created and displayed
        try {
            Field guiField = World.class.getDeclaredField("gui");
            guiField.setAccessible(true);
            WorldGUI gui = (WorldGUI) guiField.get(world);
            assertNotNull(gui, "WorldGUI should be created when GUI is enabled");
        } catch (NoSuchFieldException | IllegalAccessException e) {
            fail("Failed to verify WorldGUI: " + e.getMessage());
        }
    }
}