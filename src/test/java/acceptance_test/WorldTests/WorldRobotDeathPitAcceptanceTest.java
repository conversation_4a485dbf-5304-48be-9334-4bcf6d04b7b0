package acceptance_test.WorldTests;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.Position;
import za.co.wethinkcode.robots.config.Config;
import za.co.wethinkcode.robots.maze.Maze;
import za.co.wethinkcode.robots.robot.Robot;
import za.co.wethinkcode.robots.world.World;
import za.co.wethinkcode.robots.world.WorldGUI;
import za.co.wethinkcode.robots.UpdateResponse;
import za.co.wethinkcode.robots.OperationalStatus;
import za.co.wethinkcode.robots.Direction;

import java.lang.reflect.Field;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

// Test class for robot death from bottomless pit acceptance tests
public class WorldRobotDeathPitAcceptanceTest {

    private World world;
    private Robot testRobot;

    // Setup method to initialize the world and robot
    @BeforeEach
    void setUp() throws Exception {
        // Set configuration before World init
        setConfigField("HEIGHT", 20);
        setConfigField("WIDTH", 20);
        setConfigField("OBSTACLE_MODE", "BP-10,10:11,11"); // Single bottomless pit

        // Initialize world with GUI enabled
        world = new World(true);

        // Set test-specific Maze to ensure single obstacle
        Maze testMaze = new Maze("BP-10,10:11,11");
        assertEquals(1, testMaze.getObstacles().size(), "Test Maze should have one obstacle");
        setField(world, "maze", testMaze);
        setField(world, "obstacleList", testMaze.getObstacles());

        // Add robot at (9,10) facing east
        testRobot = createTestRobot("TestBot", new Position(9, 10));
        testRobot.updateDirection(true); // Face east
        world.addRobot(testRobot);
        world.setCurrentRobot(testRobot);
    }

    // Helper method to set Config fields using reflection
    private void setConfigField(String fieldName, Object value) throws Exception {
        Field field = Config.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(null, value);
    }

    // Helper method to set World fields using reflection
    private void setField(Object target, String fieldName, Object value) throws Exception {
        Field field = World.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }

    // Helper method to create a Robot for testing
    private Robot createTestRobot(String name, Position position) {
        Robot robot = new Robot(name, "soldier"); // Use soldier type
        robot.setPosition(position); // Set position after creation
        return robot;
    }

    // ========================================
    // Scenario d: Robot death from bottomless pit
    // ========================================
    @Test
    @DisplayName("AT-5.1.4: Test robot death when moving into bottomless pit")
    void testRobotDeathInPit() {
        // Given a robot is moving toward a bottomless pit
        assertEquals(new Position(9, 10), testRobot.getPosition(), "Robot should start at (9,10)");
        assertEquals(Direction.EAST, testRobot.getCurrentDirection(), "Robot should face east");
        assertEquals(testRobot, world.getCurrentRobot(), "Current robot should be TestBot");

        // When the robot's path intersects with the pit
        UpdateResponse result = world.updatePosition(2); // Move 2 steps east to (11,10)

        // Then the robot status should be set to DEAD
        assertEquals(OperationalStatus.DEAD, testRobot.getStatus(), "Robot status should be DEAD after hitting pit");

        // And the movement should return DIED_FELL_IN_PIT
        assertEquals(UpdateResponse.DIED_FELL_IN_PIT, result, "Movement should return DIED_FELL_IN_PIT");

        // And if GUI is enabled, the GUI should be updated to show the robot's death
        try {
            Field guiField = World.class.getDeclaredField("gui");
            guiField.setAccessible(true);
            WorldGUI gui = (WorldGUI) guiField.get(world);
            assertNotNull(gui, "WorldGUI should be created when GUI is enabled");
            // Note: gui.update() called in updatePosition; cannot directly verify without mocking
        } catch (NoSuchFieldException | IllegalAccessException e) {
            fail("Failed to verify WorldGUI: " + e.getMessage());
        }
    }
}