package acceptance_test.clienthelpermethods;

/**
 * Constants for acceptance tests
 */

public class TestConstants {
    
    // Robot Configuration
    public static final String TEST_ROBOT_NAME = "Hal";
    
    // Server Configuration
    public static final String TEST_HOST = "localhost";
    public static final int TEST_PORT = 5000;
    public static final int TIMEOUT = 5000;

    // Test Messages
    public static final String CONNECTION_MESSAGE = "Connected to the server. Initializing client robot...";
    public static final String LAUNCH_PROMPT = "Launch a robot using the following: launch <robottype> <robotname>";
    public static final String ROBOT_TYPES = "Robot types:\n  Sniper\n  Soldier\n  Hitbot<default>";

}
